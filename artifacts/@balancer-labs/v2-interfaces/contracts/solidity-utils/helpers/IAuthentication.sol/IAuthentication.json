{"_format": "hh-sol-artifact-1", "contractName": "IAuthentication", "sourceName": "@balancer-labs/v2-interfaces/contracts/solidity-utils/helpers/IAuthentication.sol", "abi": [{"inputs": [{"internalType": "bytes4", "name": "selector", "type": "bytes4"}], "name": "getActionId", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}