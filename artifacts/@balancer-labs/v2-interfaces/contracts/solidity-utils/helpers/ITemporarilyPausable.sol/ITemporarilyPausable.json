{"_format": "hh-sol-artifact-1", "contractName": "ITemporarilyPausable", "sourceName": "@balancer-labs/v2-interfaces/contracts/solidity-utils/helpers/ITemporarilyPausable.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": false, "internalType": "bool", "name": "paused", "type": "bool"}], "name": "PausedStateChanged", "type": "event"}, {"inputs": [], "name": "getPausedState", "outputs": [{"internalType": "bool", "name": "paused", "type": "bool"}, {"internalType": "uint256", "name": "pauseWindowEndTime", "type": "uint256"}, {"internalType": "uint256", "name": "bufferPeriodEndTime", "type": "uint256"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}