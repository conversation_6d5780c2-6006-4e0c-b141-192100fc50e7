{"_format": "hh-sol-artifact-1", "contractName": "IAuthorizer", "sourceName": "@balancer-labs/v2-interfaces/contracts/vault/IAuthorizer.sol", "abi": [{"inputs": [{"internalType": "bytes32", "name": "actionId", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}, {"internalType": "address", "name": "where", "type": "address"}], "name": "canPerform", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}