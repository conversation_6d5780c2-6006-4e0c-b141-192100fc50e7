{"_format": "hh-sol-artifact-1", "contractName": "IBalancer<PERSON>ault", "sourceName": "contracts/HybridFlashloanArbitrage.sol", "abi": [{"inputs": [{"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "address[]", "name": "tokens", "type": "address[]"}, {"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}, {"internalType": "bytes", "name": "userData", "type": "bytes"}], "name": "flashLoan", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}