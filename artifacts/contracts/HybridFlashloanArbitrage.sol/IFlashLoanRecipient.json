{"_format": "hh-sol-artifact-1", "contractName": "IFlashLoanRecipient", "sourceName": "contracts/HybridFlashloanArbitrage.sol", "abi": [{"inputs": [{"internalType": "address[]", "name": "tokens", "type": "address[]"}, {"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "feeAmounts", "type": "uint256[]"}, {"internalType": "bytes", "name": "userData", "type": "bytes"}], "name": "receiveFlashLoan", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}