{"_format": "hh-sol-cache-2", "files": {"/Users/<USER>/Coden/mev/bo1/contracts/FlashloanArbitrage.sol": {"lastModificationDate": 1749837147562, "contentHash": "01f1a53da0d8f92fd269df4c2efe1617", "sourceName": "contracts/FlashloanArbitrage.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@aave/core-v3/contracts/flashloan/base/FlashLoanSimpleReceiverBase.sol", "@aave/core-v3/contracts/interfaces/IPoolAddressesProvider.sol", "@openzeppelin/contracts/token/ERC20/IERC20.sol", "@openzeppelin/contracts/access/Ownable.sol"], "versionPragmas": ["^0.8.19"], "artifacts": ["FlashloanArbitrage", "IUniswapV2Router", "IUniswapV3Router"]}, "/Users/<USER>/Coden/mev/bo1/node_modules/@openzeppelin/contracts/access/Ownable.sol": {"lastModificationDate": 1749837024181, "contentHash": "d3c790edc9ccf808a17c5a6cd13614fd", "sourceName": "@openzeppelin/contracts/access/Ownable.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../utils/Context.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Ownable"]}, "/Users/<USER>/Coden/mev/bo1/node_modules/@aave/core-v3/contracts/interfaces/IPoolAddressesProvider.sol": {"lastModificationDate": 1749837023883, "contentHash": "75709782c90506d3ce968d5a6e06b900", "sourceName": "@aave/core-v3/contracts/interfaces/IPoolAddressesProvider.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["IPoolAddressesProvider"]}, "/Users/<USER>/Coden/mev/bo1/node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol": {"lastModificationDate": 1749837024178, "contentHash": "8f19f64d2adadf448840908bbaf431c8", "sourceName": "@openzeppelin/contracts/token/ERC20/IERC20.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC20"]}, "/Users/<USER>/Coden/mev/bo1/node_modules/@aave/core-v3/contracts/flashloan/base/FlashLoanSimpleReceiverBase.sol": {"lastModificationDate": 1749837023880, "contentHash": "ca03c0e2c038384aeba6f6a69a46c54b", "sourceName": "@aave/core-v3/contracts/flashloan/base/FlashLoanSimpleReceiverBase.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../interfaces/IFlashLoanSimpleReceiver.sol", "../../interfaces/IPoolAddressesProvider.sol", "../../interfaces/IPool.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["FlashLoanSimpleReceiverBase"]}, "/Users/<USER>/Coden/mev/bo1/node_modules/@openzeppelin/contracts/utils/Context.sol": {"lastModificationDate": 1749837024164, "contentHash": "67bfbc07588eb8683b3fd8f6f909563e", "sourceName": "@openzeppelin/contracts/utils/Context.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Context"]}, "/Users/<USER>/Coden/mev/bo1/node_modules/@aave/core-v3/contracts/flashloan/interfaces/IFlashLoanSimpleReceiver.sol": {"lastModificationDate": 1749837023882, "contentHash": "c12a597070a8323c6fbdc09e5322c2e2", "sourceName": "@aave/core-v3/contracts/flashloan/interfaces/IFlashLoanSimpleReceiver.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../../interfaces/IPoolAddressesProvider.sol", "../../interfaces/IPool.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["IFlashLoanSimpleReceiver"]}, "/Users/<USER>/Coden/mev/bo1/node_modules/@aave/core-v3/contracts/interfaces/IPool.sol": {"lastModificationDate": 1749837023883, "contentHash": "e977debb0018eb26ecec353110d66696", "sourceName": "@aave/core-v3/contracts/interfaces/IPool.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./IPoolAddressesProvider.sol", "../protocol/libraries/types/DataTypes.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["IPool"]}, "/Users/<USER>/Coden/mev/bo1/node_modules/@aave/core-v3/contracts/protocol/libraries/types/DataTypes.sol": {"lastModificationDate": 1749837023878, "contentHash": "5d70776caccb171510548c8c0279088e", "sourceName": "@aave/core-v3/contracts/protocol/libraries/types/DataTypes.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["DataTypes"]}, "/Users/<USER>/Coden/mev/bo1/contracts/HybridFlashloanArbitrage.sol": {"lastModificationDate": 1749837162413, "contentHash": "4585c5f04d125a0b63b47e5377fbf158", "sourceName": "contracts/HybridFlashloanArbitrage.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@aave/core-v3/contracts/flashloan/base/FlashLoanSimpleReceiverBase.sol", "@aave/core-v3/contracts/interfaces/IPoolAddressesProvider.sol", "@openzeppelin/contracts/token/ERC20/IERC20.sol", "@openzeppelin/contracts/access/Ownable.sol"], "versionPragmas": ["^0.8.19"], "artifacts": ["HybridFlashloanArbitrage", "IBalancer<PERSON>ault", "IFlashLoanRecipient", "IUniswapV2Router", "IUniswapV3Router"]}}}