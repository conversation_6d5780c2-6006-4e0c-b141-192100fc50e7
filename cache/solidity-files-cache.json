{"_format": "hh-sol-cache-2", "files": {"/Users/<USER>/Coden/mev/bo1/contracts/FlashloanArbitrage.sol": {"lastModificationDate": 1749837147562, "contentHash": "01f1a53da0d8f92fd269df4c2efe1617", "sourceName": "contracts/FlashloanArbitrage.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@aave/core-v3/contracts/flashloan/base/FlashLoanSimpleReceiverBase.sol", "@aave/core-v3/contracts/interfaces/IPoolAddressesProvider.sol", "@openzeppelin/contracts/token/ERC20/IERC20.sol", "@openzeppelin/contracts/access/Ownable.sol"], "versionPragmas": ["^0.8.19"], "artifacts": ["FlashloanArbitrage", "IUniswapV2Router", "IUniswapV3Router"]}, "/Users/<USER>/Coden/mev/bo1/node_modules/@openzeppelin/contracts/access/Ownable.sol": {"lastModificationDate": 1749837024181, "contentHash": "d3c790edc9ccf808a17c5a6cd13614fd", "sourceName": "@openzeppelin/contracts/access/Ownable.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../utils/Context.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Ownable"]}, "/Users/<USER>/Coden/mev/bo1/node_modules/@aave/core-v3/contracts/interfaces/IPoolAddressesProvider.sol": {"lastModificationDate": 1749837023883, "contentHash": "75709782c90506d3ce968d5a6e06b900", "sourceName": "@aave/core-v3/contracts/interfaces/IPoolAddressesProvider.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["IPoolAddressesProvider"]}, "/Users/<USER>/Coden/mev/bo1/node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol": {"lastModificationDate": 1749837024178, "contentHash": "8f19f64d2adadf448840908bbaf431c8", "sourceName": "@openzeppelin/contracts/token/ERC20/IERC20.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC20"]}, "/Users/<USER>/Coden/mev/bo1/node_modules/@aave/core-v3/contracts/flashloan/base/FlashLoanSimpleReceiverBase.sol": {"lastModificationDate": 1749837023880, "contentHash": "ca03c0e2c038384aeba6f6a69a46c54b", "sourceName": "@aave/core-v3/contracts/flashloan/base/FlashLoanSimpleReceiverBase.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../interfaces/IFlashLoanSimpleReceiver.sol", "../../interfaces/IPoolAddressesProvider.sol", "../../interfaces/IPool.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["FlashLoanSimpleReceiverBase"]}, "/Users/<USER>/Coden/mev/bo1/node_modules/@openzeppelin/contracts/utils/Context.sol": {"lastModificationDate": 1749837024164, "contentHash": "67bfbc07588eb8683b3fd8f6f909563e", "sourceName": "@openzeppelin/contracts/utils/Context.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Context"]}, "/Users/<USER>/Coden/mev/bo1/node_modules/@aave/core-v3/contracts/flashloan/interfaces/IFlashLoanSimpleReceiver.sol": {"lastModificationDate": 1749837023882, "contentHash": "c12a597070a8323c6fbdc09e5322c2e2", "sourceName": "@aave/core-v3/contracts/flashloan/interfaces/IFlashLoanSimpleReceiver.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../../interfaces/IPoolAddressesProvider.sol", "../../interfaces/IPool.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["IFlashLoanSimpleReceiver"]}, "/Users/<USER>/Coden/mev/bo1/node_modules/@aave/core-v3/contracts/interfaces/IPool.sol": {"lastModificationDate": 1749837023883, "contentHash": "e977debb0018eb26ecec353110d66696", "sourceName": "@aave/core-v3/contracts/interfaces/IPool.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./IPoolAddressesProvider.sol", "../protocol/libraries/types/DataTypes.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["IPool"]}, "/Users/<USER>/Coden/mev/bo1/node_modules/@aave/core-v3/contracts/protocol/libraries/types/DataTypes.sol": {"lastModificationDate": 1749837023878, "contentHash": "5d70776caccb171510548c8c0279088e", "sourceName": "@aave/core-v3/contracts/protocol/libraries/types/DataTypes.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["DataTypes"]}, "/Users/<USER>/Coden/mev/bo1/contracts/HybridFlashloanArbitrage.sol": {"lastModificationDate": 1749837708863, "contentHash": "2cc3ee967e4e74e6a3308b6492701829", "sourceName": "contracts/HybridFlashloanArbitrage.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@aave/core-v3/contracts/flashloan/base/FlashLoanSimpleReceiverBase.sol", "@aave/core-v3/contracts/interfaces/IPoolAddressesProvider.sol", "@openzeppelin/contracts/access/Ownable.sol", "@balancer-labs/v2-interfaces/contracts/vault/IVault.sol", "@balancer-labs/v2-interfaces/contracts/vault/IFlashLoanRecipient.sol", "@balancer-labs/v2-interfaces/contracts/solidity-utils/openzeppelin/IERC20.sol"], "versionPragmas": ["^0.8.19"], "artifacts": ["HybridFlashloanArbitrage", "IUniswapV2Router", "IUniswapV3Router"]}, "/Users/<USER>/Coden/mev/bo1/contracts/BalancerFlashloanArbitrage.sol": {"lastModificationDate": 1749837688916, "contentHash": "05cb2f4afac65d656d9dd83d4f7f05aa", "sourceName": "contracts/BalancerFlashloanArbitrage.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@balancer-labs/v2-interfaces/contracts/vault/IVault.sol", "@balancer-labs/v2-interfaces/contracts/vault/IFlashLoanRecipient.sol", "@balancer-labs/v2-interfaces/contracts/solidity-utils/openzeppelin/IERC20.sol", "@openzeppelin/contracts/access/Ownable.sol"], "versionPragmas": ["^0.8.19"], "artifacts": ["BalancerFlashloanArbitrage", "IUniswapV2Router", "IUniswapV3Router"]}, "/Users/<USER>/Coden/mev/bo1/node_modules/@balancer-labs/v2-interfaces/contracts/vault/IFlashLoanRecipient.sol": {"lastModificationDate": 1749837494352, "contentHash": "31e2c769fc0ac25db2d9b46225793f01", "sourceName": "@balancer-labs/v2-interfaces/contracts/vault/IFlashLoanRecipient.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../solidity-utils/openzeppelin/IERC20.sol"], "versionPragmas": [">=0.7.0 <0.9.0"], "artifacts": ["IFlashLoanRecipient"]}, "/Users/<USER>/Coden/mev/bo1/node_modules/@balancer-labs/v2-interfaces/contracts/vault/IVault.sol": {"lastModificationDate": 1749837494354, "contentHash": "46a20a7701464ab9c62cb907614fd3bb", "sourceName": "@balancer-labs/v2-interfaces/contracts/vault/IVault.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../solidity-utils/openzeppelin/IERC20.sol", "../solidity-utils/helpers/IAuthentication.sol", "../solidity-utils/helpers/ISignaturesValidator.sol", "../solidity-utils/helpers/ITemporarilyPausable.sol", "../solidity-utils/misc/IWETH.sol", "./IAsset.sol", "./IAuthorizer.sol", "./IFlashLoanRecipient.sol", "./IProtocolFeesCollector.sol"], "versionPragmas": [">=0.7.0 <0.9.0"], "artifacts": ["IVault"]}, "/Users/<USER>/Coden/mev/bo1/node_modules/@balancer-labs/v2-interfaces/contracts/solidity-utils/openzeppelin/IERC20.sol": {"lastModificationDate": 1749837494345, "contentHash": "028dddcf9b178b241401b1ae8126c903", "sourceName": "@balancer-labs/v2-interfaces/contracts/solidity-utils/openzeppelin/IERC20.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": [">=0.7.0 <0.9.0"], "artifacts": ["IERC20"]}, "/Users/<USER>/Coden/mev/bo1/node_modules/@balancer-labs/v2-interfaces/contracts/solidity-utils/helpers/ISignaturesValidator.sol": {"lastModificationDate": 1749837494343, "contentHash": "48de5f36515898c9e9ddcd00f651ef86", "sourceName": "@balancer-labs/v2-interfaces/contracts/solidity-utils/helpers/ISignaturesValidator.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": [">=0.7.0 <0.9.0"], "artifacts": ["ISignaturesValidator"]}, "/Users/<USER>/Coden/mev/bo1/node_modules/@balancer-labs/v2-interfaces/contracts/solidity-utils/helpers/IAuthentication.sol": {"lastModificationDate": 1749837494343, "contentHash": "63f8c7b7a4ac329c8104063a025a991b", "sourceName": "@balancer-labs/v2-interfaces/contracts/solidity-utils/helpers/IAuthentication.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": [">=0.7.0 <0.9.0"], "artifacts": ["IAuthentication"]}, "/Users/<USER>/Coden/mev/bo1/node_modules/@balancer-labs/v2-interfaces/contracts/solidity-utils/helpers/ITemporarilyPausable.sol": {"lastModificationDate": 1749837494343, "contentHash": "5a94e6420fe95c3e0fa16e78fb8f74c3", "sourceName": "@balancer-labs/v2-interfaces/contracts/solidity-utils/helpers/ITemporarilyPausable.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": [">=0.7.0 <0.9.0"], "artifacts": ["ITemporarilyPausable"]}, "/Users/<USER>/Coden/mev/bo1/node_modules/@balancer-labs/v2-interfaces/contracts/solidity-utils/misc/IWETH.sol": {"lastModificationDate": 1749837494344, "contentHash": "8347e7eef0bb47de8851ecbcf8eb590c", "sourceName": "@balancer-labs/v2-interfaces/contracts/solidity-utils/misc/IWETH.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../openzeppelin/IERC20.sol"], "versionPragmas": [">=0.7.0 <0.9.0"], "artifacts": ["IWETH"]}, "/Users/<USER>/Coden/mev/bo1/node_modules/@balancer-labs/v2-interfaces/contracts/vault/IAsset.sol": {"lastModificationDate": 1749837494352, "contentHash": "20c61578e7416055512409a25576fb1a", "sourceName": "@balancer-labs/v2-interfaces/contracts/vault/IAsset.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": [">=0.7.0 <0.9.0"], "artifacts": ["IAsset"]}, "/Users/<USER>/Coden/mev/bo1/node_modules/@balancer-labs/v2-interfaces/contracts/vault/IProtocolFeesCollector.sol": {"lastModificationDate": 1749837494353, "contentHash": "26a23f2667431cfc1ea8aa5e69edef63", "sourceName": "@balancer-labs/v2-interfaces/contracts/vault/IProtocolFeesCollector.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../solidity-utils/openzeppelin/IERC20.sol", "./IVault.sol", "./IAuthorizer.sol"], "versionPragmas": [">=0.7.0 <0.9.0"], "artifacts": ["IProtocolFeesCollector"]}, "/Users/<USER>/Coden/mev/bo1/node_modules/@balancer-labs/v2-interfaces/contracts/vault/IAuthorizer.sol": {"lastModificationDate": 1749837494352, "contentHash": "03599610995051c98edd4f72102136b2", "sourceName": "@balancer-labs/v2-interfaces/contracts/vault/IAuthorizer.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": [">=0.7.0 <0.9.0"], "artifacts": ["IAuthorizer"]}}}