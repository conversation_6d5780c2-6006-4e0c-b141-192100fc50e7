// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@aave/core-v3/contracts/flashloan/base/FlashLoanSimpleReceiverBase.sol";
import "@aave/core-v3/contracts/interfaces/IPoolAddressesProvider.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";

interface IUniswapV2Router {
    function swapExactTokensForTokens(
        uint amountIn,
        uint amountOutMin,
        address[] calldata path,
        address to,
        uint deadline
    ) external returns (uint[] memory amounts);
    
    function getAmountsOut(uint amountIn, address[] calldata path)
        external view returns (uint[] memory amounts);
}

interface IUniswapV3Router {
    struct ExactInputSingleParams {
        address tokenIn;
        address tokenOut;
        uint24 fee;
        address recipient;
        uint256 deadline;
        uint256 amountIn;
        uint256 amountOutMinimum;
        uint160 sqrtPriceLimitX96;
    }

    function exactInputSingle(ExactInputSingleParams calldata params)
        external payable returns (uint256 amountOut);
}

/**
 * @title FlashloanArbitrage
 * @dev Contract to execute flashloan-based arbitrage between DEXs
 */
contract FlashloanArbitrage is FlashLoanSimpleReceiverBase, Ownable {

    // Network-specific router addresses (set in constructor)
    address public immutable UNISWAP_V2_ROUTER;
    address public immutable UNISWAP_V3_ROUTER;
    address public immutable SUSHISWAP_ROUTER;

    // Network detection
    uint256 public immutable CHAIN_ID;
    
    struct ArbitrageParams {
        address tokenA;
        address tokenB;
        address buyDex;  // Router address for buying
        address sellDex; // Router address for selling
        uint24 v3Fee;   // Fee for V3 pools (if applicable)
        uint256 minProfit;
    }
    
    event ArbitrageExecuted(
        address indexed asset,
        uint256 amount,
        uint256 profit
    );
    
    event FlashloanExecuted(
        address indexed asset,
        uint256 amount,
        uint256 premium
    );

    constructor(IPoolAddressesProvider _addressProvider)
        FlashLoanSimpleReceiverBase(_addressProvider)
        Ownable(msg.sender)
    {
        CHAIN_ID = block.chainid;

        if (block.chainid == 1) {
            // Mainnet addresses
            UNISWAP_V2_ROUTER = 0x7a250d5630B4cF539739dF2C5dAcb4c659F2488D;
            UNISWAP_V3_ROUTER = 0xE592427A0AEce92De3Edee1F18E0157C05861564;
            SUSHISWAP_ROUTER = 0xd9e1cE17f2641f24aE83637ab66a2cca9C378B9F;
        } else if (block.chainid == 11155111) {
            // Sepolia addresses
            UNISWAP_V2_ROUTER = 0x86dcd3293C53Cf8EFd7303B57beb2a3F671dDE98;
            UNISWAP_V3_ROUTER = 0x3bFA4769FB09eefC5a80d6E87c3B9C650f7Ae48E;
            SUSHISWAP_ROUTER = address(0); // Not available on Sepolia
        } else {
            revert("Unsupported network");
        }
    }

    /**
     * @dev Execute flashloan arbitrage
     * @param asset The asset to flashloan
     * @param amount The amount to flashloan
     * @param params Encoded arbitrage parameters
     */
    function executeFlashloanArbitrage(
        address asset,
        uint256 amount,
        bytes calldata params
    ) external onlyOwner {
        // Decode arbitrage parameters
        ArbitrageParams memory arbParams = abi.decode(params, (ArbitrageParams));
        
        // Validate parameters
        require(asset == arbParams.tokenA, "Asset mismatch");
        require(amount > 0, "Invalid amount");
        
        // Execute flashloan
        POOL.flashLoanSimple(
            address(this),
            asset,
            amount,
            params,
            0 // referralCode
        );
    }

    /**
     * @dev Called by Aave pool after receiving the flashloan
     * @param asset The asset that was borrowed
     * @param amount The amount that was borrowed
     * @param premium The fee for the flashloan
     * @param initiator The address that initiated the flashloan
     * @param params The encoded arbitrage parameters
     */
    function executeOperation(
        address asset,
        uint256 amount,
        uint256 premium,
        address initiator,
        bytes calldata params
    ) external override returns (bool) {
        // Ensure this is called by the Aave pool
        require(msg.sender == address(POOL), "Caller must be pool");
        require(initiator == address(this), "Invalid initiator");
        
        // Decode arbitrage parameters
        ArbitrageParams memory arbParams = abi.decode(params, (ArbitrageParams));
        
        // Execute arbitrage
        uint256 profit = _executeArbitrage(asset, amount, arbParams);
        
        // Calculate total amount to repay (amount + premium)
        uint256 amountToRepay = amount + premium;
        
        // Ensure we have enough to repay
        require(
            IERC20(asset).balanceOf(address(this)) >= amountToRepay,
            "Insufficient balance to repay flashloan"
        );
        
        // Ensure we made profit
        require(profit > premium, "Arbitrage not profitable");
        
        // Approve the pool to pull the repayment
        IERC20(asset).approve(address(POOL), amountToRepay);
        
        emit FlashloanExecuted(asset, amount, premium);
        emit ArbitrageExecuted(asset, amount, profit - premium);
        
        return true;
    }

    /**
     * @dev Execute the arbitrage between two DEXs
     * @param asset The asset being arbitraged
     * @param amount The amount to arbitrage
     * @param params The arbitrage parameters
     * @return profit The profit made from arbitrage
     */
    function _executeArbitrage(
        address asset,
        uint256 amount,
        ArbitrageParams memory params
    ) internal returns (uint256 profit) {
        uint256 initialBalance = IERC20(asset).balanceOf(address(this));
        
        // Step 1: Buy tokenB on the cheaper DEX
        uint256 tokenBAmount = _executeBuy(asset, amount, params);
        
        // Step 2: Sell tokenB on the more expensive DEX
        uint256 finalAmount = _executeSell(params.tokenB, tokenBAmount, params);
        
        uint256 finalBalance = IERC20(asset).balanceOf(address(this));
        profit = finalBalance - initialBalance;
        
        return profit;
    }

    /**
     * @dev Execute buy operation on DEX
     */
    function _executeBuy(
        address tokenIn,
        uint256 amountIn,
        ArbitrageParams memory params
    ) internal returns (uint256 amountOut) {
        // Approve the router to spend tokens
        IERC20(tokenIn).approve(params.buyDex, amountIn);
        
        if (params.buyDex == UNISWAP_V2_ROUTER) {
            // Execute V2 swap
            address[] memory path = new address[](2);
            path[0] = tokenIn;
            path[1] = params.tokenB;
            
            uint[] memory amounts = IUniswapV2Router(params.buyDex)
                .swapExactTokensForTokens(
                    amountIn,
                    0, // Accept any amount of tokens out
                    path,
                    address(this),
                    block.timestamp + 300
                );
            
            amountOut = amounts[1];
        } else {
            // Execute V3 swap
            IUniswapV3Router.ExactInputSingleParams memory swapParams = 
                IUniswapV3Router.ExactInputSingleParams({
                    tokenIn: tokenIn,
                    tokenOut: params.tokenB,
                    fee: params.v3Fee,
                    recipient: address(this),
                    deadline: block.timestamp + 300,
                    amountIn: amountIn,
                    amountOutMinimum: 0,
                    sqrtPriceLimitX96: 0
                });
            
            amountOut = IUniswapV3Router(params.buyDex).exactInputSingle(swapParams);
        }
        
        return amountOut;
    }

    /**
     * @dev Execute sell operation on DEX
     */
    function _executeSell(
        address tokenIn,
        uint256 amountIn,
        ArbitrageParams memory params
    ) internal returns (uint256 amountOut) {
        // Approve the router to spend tokens
        IERC20(tokenIn).approve(params.sellDex, amountIn);
        
        if (params.sellDex == UNISWAP_V2_ROUTER) {
            // Execute V2 swap
            address[] memory path = new address[](2);
            path[0] = tokenIn;
            path[1] = params.tokenA;
            
            uint[] memory amounts = IUniswapV2Router(params.sellDex)
                .swapExactTokensForTokens(
                    amountIn,
                    0, // Accept any amount of tokens out
                    path,
                    address(this),
                    block.timestamp + 300
                );
            
            amountOut = amounts[1];
        } else {
            // Execute V3 swap
            IUniswapV3Router.ExactInputSingleParams memory swapParams = 
                IUniswapV3Router.ExactInputSingleParams({
                    tokenIn: tokenIn,
                    tokenOut: params.tokenA,
                    fee: params.v3Fee,
                    recipient: address(this),
                    deadline: block.timestamp + 300,
                    amountIn: amountIn,
                    amountOutMinimum: 0,
                    sqrtPriceLimitX96: 0
                });
            
            amountOut = IUniswapV3Router(params.sellDex).exactInputSingle(swapParams);
        }
        
        return amountOut;
    }

    /**
     * @dev Withdraw profits to owner
     */
    function withdrawProfits(address token) external onlyOwner {
        uint256 balance = IERC20(token).balanceOf(address(this));
        require(balance > 0, "No profits to withdraw");
        IERC20(token).transfer(owner(), balance);
    }

    /**
     * @dev Emergency function to withdraw any stuck tokens
     */
    function emergencyWithdraw(address token, uint256 amount) external onlyOwner {
        IERC20(token).transfer(owner(), amount);
    }
}
