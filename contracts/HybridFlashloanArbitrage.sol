// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@aave/core-v3/contracts/flashloan/base/FlashLoanSimpleReceiverBase.sol";
import "@aave/core-v3/contracts/interfaces/IPoolAddressesProvider.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";

// Balancer V2 interfaces
interface IBalancerVault {
    function flashLoan(
        address recipient,
        address[] memory tokens,
        uint256[] memory amounts,
        bytes memory userData
    ) external;
}

interface IFlashLoanRecipient {
    function receiveFlashLoan(
        address[] memory tokens,
        uint256[] memory amounts,
        uint256[] memory feeAmounts,
        bytes memory userData
    ) external;
}

interface IUniswapV2Router {
    function swapExactTokensForTokens(
        uint amountIn,
        uint amountOutMin,
        address[] calldata path,
        address to,
        uint deadline
    ) external returns (uint[] memory amounts);
}

interface IUniswapV3Router {
    struct ExactInputSingleParams {
        address tokenIn;
        address tokenOut;
        uint24 fee;
        address recipient;
        uint256 deadline;
        uint256 amountIn;
        uint256 amountOutMinimum;
        uint160 sqrtPriceLimitX96;
    }

    function exactInputSingle(ExactInputSingleParams calldata params)
        external payable returns (uint256 amountOut);
}

/**
 * @title HybridFlashloanArbitrage
 * @dev Contract that can execute flashloans from both Aave and Balancer
 */
contract HybridFlashloanArbitrage is FlashLoanSimpleReceiverBase, IFlashLoanRecipient, Ownable {
    
    // Network-specific addresses
    address public immutable UNISWAP_V2_ROUTER;
    address public immutable UNISWAP_V3_ROUTER;
    address public immutable BALANCER_VAULT;
    uint256 public immutable CHAIN_ID;
    
    enum FlashloanProvider { AAVE, BALANCER }
    
    struct ArbitrageParams {
        address tokenA;
        address tokenB;
        address buyDex;
        address sellDex;
        uint24 v3Fee;
        uint256 minProfit;
        FlashloanProvider provider;
    }
    
    event FlashloanExecuted(
        FlashloanProvider indexed provider,
        address indexed asset,
        uint256 amount,
        uint256 premium,
        uint256 profit
    );
    
    event ArbitrageCompleted(
        address indexed tokenA,
        address indexed tokenB,
        uint256 profit
    );

    constructor(
        IPoolAddressesProvider _aaveAddressProvider,
        address _balancerVault
    )
        FlashLoanSimpleReceiverBase(_aaveAddressProvider)
        Ownable(msg.sender)
    {
        CHAIN_ID = block.chainid;
        BALANCER_VAULT = _balancerVault;

        // Set router addresses based on chain ID
        (UNISWAP_V2_ROUTER, UNISWAP_V3_ROUTER) = _getRouterAddresses(block.chainid);
    }

    function _getRouterAddresses(uint256 chainId) private pure returns (address, address) {
        if (chainId == 1) {
            // Mainnet addresses
            return (
                0x7a250d5630B4cF539739dF2C5dAcb4c659F2488D, // Uniswap V2
                0xE592427A0AEce92De3Edee1F18E0157C05861564   // Uniswap V3
            );
        } else if (chainId == 11155111) {
            // Sepolia addresses
            return (
                0x86dcd3293C53Cf8EFd7303B57beb2a3F671dDE98, // Uniswap V2
                0x3bFA4769FB09eefC5a80d6E87c3B9C650f7Ae48E  // Uniswap V3
            );
        } else {
            revert("Unsupported network");
        }
    }

    /**
     * @dev Execute flashloan arbitrage using the optimal provider
     * @param asset The asset to flashloan
     * @param amount The amount to flashloan
     * @param params Encoded arbitrage parameters
     */
    function executeOptimalFlashloan(
        address asset,
        uint256 amount,
        bytes calldata params
    ) external onlyOwner {
        ArbitrageParams memory arbParams = abi.decode(params, (ArbitrageParams));
        
        require(asset == arbParams.tokenA, "Asset mismatch");
        require(amount > 0, "Invalid amount");
        
        if (arbParams.provider == FlashloanProvider.BALANCER) {
            _executeBalancerFlashloan(asset, amount, params);
        } else {
            _executeAaveFlashloan(asset, amount, params);
        }
    }

    /**
     * @dev Execute Balancer flashloan (0% fees!)
     */
    function _executeBalancerFlashloan(
        address asset,
        uint256 amount,
        bytes memory params
    ) internal {
        address[] memory tokens = new address[](1);
        tokens[0] = asset;
        
        uint256[] memory amounts = new uint256[](1);
        amounts[0] = amount;
        
        IBalancerVault(BALANCER_VAULT).flashLoan(
            address(this),
            tokens,
            amounts,
            params
        );
    }

    /**
     * @dev Execute Aave flashloan (0.09% fees)
     */
    function _executeAaveFlashloan(
        address asset,
        uint256 amount,
        bytes memory params
    ) internal {
        POOL.flashLoanSimple(
            address(this),
            asset,
            amount,
            params,
            0 // referralCode
        );
    }

    /**
     * @dev Balancer flashloan callback (0% fees!)
     */
    function receiveFlashLoan(
        address[] memory tokens,
        uint256[] memory amounts,
        uint256[] memory feeAmounts, // Always 0 for Balancer!
        bytes memory userData
    ) external override {
        require(msg.sender == BALANCER_VAULT, "Caller must be Balancer Vault");
        
        ArbitrageParams memory arbParams = abi.decode(userData, (ArbitrageParams));
        
        // Execute arbitrage
        uint256 profit = _executeArbitrage(tokens[0], amounts[0], arbParams);
        
        // Repay flashloan (no fees for Balancer!)
        uint256 amountToRepay = amounts[0]; // No premium!
        
        require(
            IERC20(tokens[0]).balanceOf(address(this)) >= amountToRepay,
            "Insufficient balance to repay Balancer flashloan"
        );
        
        // Transfer tokens back to Balancer Vault
        IERC20(tokens[0]).transfer(BALANCER_VAULT, amountToRepay);
        
        emit FlashloanExecuted(
            FlashloanProvider.BALANCER,
            tokens[0],
            amounts[0],
            0, // No premium for Balancer!
            profit
        );
    }

    /**
     * @dev Aave flashloan callback (0.09% fees)
     */
    function executeOperation(
        address asset,
        uint256 amount,
        uint256 premium,
        address initiator,
        bytes calldata params
    ) external override returns (bool) {
        require(msg.sender == address(POOL), "Caller must be Aave pool");
        require(initiator == address(this), "Invalid initiator");
        
        ArbitrageParams memory arbParams = abi.decode(params, (ArbitrageParams));
        
        // Execute arbitrage
        uint256 profit = _executeArbitrage(asset, amount, arbParams);
        
        // Repay flashloan with premium
        uint256 amountToRepay = amount + premium;
        
        require(
            IERC20(asset).balanceOf(address(this)) >= amountToRepay,
            "Insufficient balance to repay Aave flashloan"
        );
        
        // Approve Aave pool to pull repayment
        IERC20(asset).approve(address(POOL), amountToRepay);
        
        require(profit > premium, "Arbitrage not profitable after Aave fees");
        
        emit FlashloanExecuted(
            FlashloanProvider.AAVE,
            asset,
            amount,
            premium,
            profit - premium
        );
        
        return true;
    }

    /**
     * @dev Execute arbitrage between two DEXs
     */
    function _executeArbitrage(
        address asset,
        uint256 amount,
        ArbitrageParams memory params
    ) internal returns (uint256 profit) {
        uint256 initialBalance = IERC20(asset).balanceOf(address(this));
        
        // Step 1: Buy tokenB on cheaper DEX
        uint256 tokenBAmount = _executeBuy(asset, amount, params);
        
        // Step 2: Sell tokenB on more expensive DEX
        uint256 finalAmount = _executeSell(params.tokenB, tokenBAmount, params);
        
        uint256 finalBalance = IERC20(asset).balanceOf(address(this));
        profit = finalBalance - initialBalance;
        
        emit ArbitrageCompleted(params.tokenA, params.tokenB, profit);
        
        return profit;
    }

    /**
     * @dev Execute buy operation
     */
    function _executeBuy(
        address tokenIn,
        uint256 amountIn,
        ArbitrageParams memory params
    ) internal returns (uint256 amountOut) {
        IERC20(tokenIn).approve(params.buyDex, amountIn);
        
        if (params.buyDex == UNISWAP_V2_ROUTER) {
            // V2 swap
            address[] memory path = new address[](2);
            path[0] = tokenIn;
            path[1] = params.tokenB;
            
            uint[] memory amounts = IUniswapV2Router(params.buyDex)
                .swapExactTokensForTokens(
                    amountIn,
                    0,
                    path,
                    address(this),
                    block.timestamp + 300
                );
            
            amountOut = amounts[1];
        } else {
            // V3 swap
            IUniswapV3Router.ExactInputSingleParams memory swapParams = 
                IUniswapV3Router.ExactInputSingleParams({
                    tokenIn: tokenIn,
                    tokenOut: params.tokenB,
                    fee: params.v3Fee,
                    recipient: address(this),
                    deadline: block.timestamp + 300,
                    amountIn: amountIn,
                    amountOutMinimum: 0,
                    sqrtPriceLimitX96: 0
                });
            
            amountOut = IUniswapV3Router(params.buyDex).exactInputSingle(swapParams);
        }
        
        return amountOut;
    }

    /**
     * @dev Execute sell operation
     */
    function _executeSell(
        address tokenIn,
        uint256 amountIn,
        ArbitrageParams memory params
    ) internal returns (uint256 amountOut) {
        IERC20(tokenIn).approve(params.sellDex, amountIn);
        
        if (params.sellDex == UNISWAP_V2_ROUTER) {
            // V2 swap
            address[] memory path = new address[](2);
            path[0] = tokenIn;
            path[1] = params.tokenA;
            
            uint[] memory amounts = IUniswapV2Router(params.sellDex)
                .swapExactTokensForTokens(
                    amountIn,
                    0,
                    path,
                    address(this),
                    block.timestamp + 300
                );
            
            amountOut = amounts[1];
        } else {
            // V3 swap
            IUniswapV3Router.ExactInputSingleParams memory swapParams = 
                IUniswapV3Router.ExactInputSingleParams({
                    tokenIn: tokenIn,
                    tokenOut: params.tokenA,
                    fee: params.v3Fee,
                    recipient: address(this),
                    deadline: block.timestamp + 300,
                    amountIn: amountIn,
                    amountOutMinimum: 0,
                    sqrtPriceLimitX96: 0
                });
            
            amountOut = IUniswapV3Router(params.sellDex).exactInputSingle(swapParams);
        }
        
        return amountOut;
    }

    /**
     * @dev Choose optimal flashloan provider based on amount and market conditions
     */
    function getOptimalProvider(
        address asset,
        uint256 amount
    ) external view returns (FlashloanProvider) {
        // For smaller amounts, Balancer is always better (0% fees)
        // For larger amounts, check if Balancer has enough liquidity
        
        if (amount <= 50000 * 10**6) { // 50k USDC or equivalent
            return FlashloanProvider.BALANCER;
        }
        
        // For larger amounts, would need to check Balancer liquidity
        // For now, default to Aave for large amounts
        return FlashloanProvider.AAVE;
    }

    /**
     * @dev Withdraw profits
     */
    function withdrawProfits(address token) external onlyOwner {
        uint256 balance = IERC20(token).balanceOf(address(this));
        require(balance > 0, "No profits to withdraw");
        IERC20(token).transfer(owner(), balance);
    }

    /**
     * @dev Emergency withdraw
     */
    function emergencyWithdraw(address token, uint256 amount) external onlyOwner {
        IERC20(token).transfer(owner(), amount);
    }
}
