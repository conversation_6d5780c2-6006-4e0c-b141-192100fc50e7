require("dotenv").config();
const { ethers } = require("hardhat");

async function main() {
    const signer = (await ethers.getSigners())[0];
    const usdcAddress = "******************************************";
    const usdc = await ethers.getContractAt("IERC20", usdcAddress, signer);

    const tx = await usdc.transfer(signer.address, ethers.utils.parseUnits("1000", 6));
    await tx.wait();

    console.log("1.000 USDC Testnet-Token gesendet an:", signer.address);
}

main().catch(console.error);
