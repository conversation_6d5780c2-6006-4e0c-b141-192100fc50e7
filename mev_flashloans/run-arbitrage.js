require("dotenv").config();
const hre = require("hardhat");

async function main() {
    const contractAddress = "DEIN_DEPLOYTER_CONTRACT"; // nach deploy.js ersetzen
    const usdcAddress = "******************************************";

    const contract = await hre.ethers.getContractAt("FlashloanArbitrage", contractAddress);
    const amount = hre.ethers.utils.parseUnits("1000", 6); // 1.000 USDC

    const tx = await contract.startArbitrage(usdcAddress, amount);
    console.log("Arbitrage gestartet... tx:", tx.hash);
    await tx.wait();
    console.log("Fertig!");
}

main().catch((error) => {
    console.error(error);
    process.exitCode = 1;
});
