const { ethers } = require("hardhat");

async function main() {
  console.log("🚀 Deploying HybridFlashloanArbitrage contract...");

  // Network-specific addresses
  const network = await ethers.provider.getNetwork();
  const chainId = Number(network.chainId);
  
  let AAVE_POOL_ADDRESSES_PROVIDER;
  let BALANCER_VAULT;
  let networkName;

  if (chainId === 1) {
    // Mainnet
    AAVE_POOL_ADDRESSES_PROVIDER = "******************************************";
    BALANCER_VAULT = "******************************************";
    networkName = "Mainnet";
  } else if (chainId === 11155111) {
    // Sepolia
    AAVE_POOL_ADDRESSES_PROVIDER = "******************************************";
    BALANCER_VAULT = "******************************************";
    networkName = "Sepolia";
  } else {
    console.error(`❌ Unsupported network: Chain ID ${chainId}`);
    process.exit(1);
  }

  console.log(`📋 Deployment Details:`);
  console.log(`Network: ${networkName} (Chain ID: ${chainId})`);
  console.log(`Aave Pool Provider: ${AAVE_POOL_ADDRESSES_PROVIDER}`);
  console.log(`Balancer Vault: ${BALANCER_VAULT}`);

  // Get deployer account
  const [deployer] = await ethers.getSigners();
  const deployerBalance = await ethers.provider.getBalance(deployer.address);
  
  console.log(`Deployer: ${deployer.address}`);
  console.log(`Balance: ${ethers.formatEther(deployerBalance)} ETH`);

  // Check minimum balance
  const minBalance = ethers.parseEther(chainId === 1 ? "0.1" : "0.01");
  if (deployerBalance < minBalance) {
    console.error(`❌ Insufficient balance. Need at least ${ethers.formatEther(minBalance)} ETH.`);
    process.exit(1);
  }

  // Get the contract factory
  const HybridFlashloanArbitrage = await ethers.getContractFactory("HybridFlashloanArbitrage");

  // Deploy the contract
  console.log("📝 Deploying contract...");
  const hybridFlashloan = await HybridFlashloanArbitrage.deploy(
    AAVE_POOL_ADDRESSES_PROVIDER,
    BALANCER_VAULT
  );

  console.log("⏳ Waiting for deployment confirmation...");
  await hybridFlashloan.waitForDeployment();

  const contractAddress = await hybridFlashloan.getAddress();
  console.log("✅ HybridFlashloanArbitrage deployed successfully!");

  // Verify deployment
  console.log("🔍 Verifying deployment...");
  const owner = await hybridFlashloan.owner();
  const chainIdContract = await hybridFlashloan.CHAIN_ID();
  const v2Router = await hybridFlashloan.UNISWAP_V2_ROUTER();
  const v3Router = await hybridFlashloan.UNISWAP_V3_ROUTER();
  const balancerVault = await hybridFlashloan.BALANCER_VAULT();

  console.log("\n📊 Contract Verification:");
  console.log("=".repeat(50));
  console.log(`Contract Address: ${contractAddress}`);
  console.log(`Owner: ${owner}`);
  console.log(`Chain ID: ${chainIdContract}`);
  console.log(`Uniswap V2 Router: ${v2Router}`);
  console.log(`Uniswap V3 Router: ${v3Router}`);
  console.log(`Balancer Vault: ${balancerVault}`);
  console.log("=".repeat(50));

  // Save deployment info
  const deploymentInfo = {
    network: networkName.toLowerCase(),
    chainId: chainId,
    contractAddress: contractAddress,
    owner: owner,
    aavePoolAddressesProvider: AAVE_POOL_ADDRESSES_PROVIDER,
    balancerVault: BALANCER_VAULT,
    deployedAt: new Date().toISOString(),
    routers: {
      uniswapV2: v2Router,
      uniswapV3: v3Router
    }
  };

  // Write deployment info to file
  const fs = require('fs');
  const filename = `deployment-hybrid-${networkName.toLowerCase()}.json`;
  fs.writeFileSync(filename, JSON.stringify(deploymentInfo, null, 2));

  console.log("\n🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!");
  console.log("=".repeat(50));
  console.log("🔧 NEXT STEPS:");
  console.log("1. Update your .env file:");
  console.log(`   HYBRID_FLASHLOAN_CONTRACT=${contractAddress}`);
  console.log("2. Fund the contract with ETH for gas fees");
  console.log("3. Test with DRY_RUN=true first");
  console.log("4. Enable hybrid flashloan attacks:");
  console.log("   ENABLE_FLASHLOAN_ATTACKS=true");
  console.log("   ENABLE_BALANCER_FLASHLOANS=true");
  console.log("");
  console.log("💡 HYBRID ADVANTAGES:");
  console.log("- 🔵 Balancer: 0% fees for smaller amounts");
  console.log("- 🟠 Aave: Better liquidity for larger amounts");
  console.log("- 🔄 Automatic provider selection");
  console.log("- 📈 Up to 60% more profit potential");
  console.log("");
  console.log(`📄 Deployment details saved to: ${filename}`);
  console.log("=".repeat(50));

  return contractAddress;
}

// Handle errors
main()
  .then((contractAddress) => {
    console.log(`\n🎉 Hybrid flashloan deployment completed!`);
    console.log(`Contract Address: ${contractAddress}`);
    process.exit(0);
  })
  .catch((error) => {
    console.error("❌ Deployment failed:", error);
    process.exit(1);
  });
