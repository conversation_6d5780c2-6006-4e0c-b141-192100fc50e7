import dotenv from 'dotenv';
import { Config } from '../types';

dotenv.config();

export const config: Config = {
  rpcUrl: process.env.RPC_URL || 'https://eth-sepolia.g.alchemy.com/v2/YOUR_ALCHEMY_KEY',
  flashbotsRpcUrl: process.env.FLASHBOTS_RPC_URL || 'https://relay.flashbots.net',
  chainId: parseInt(process.env.CHAIN_ID || '11155111'), // Default to Sepolia
  privateKey: process.env.PRIVATE_KEY || '0x0000000000000000000000000000000000000000000000000000000000000000',
  flashbotsSignerKey: process.env.FLASHBOTS_SIGNER_KEY || '0x0000000000000000000000000000000000000000000000000000000000000000',
  minProfitWei: process.env.MIN_PROFIT_WEI || '1000000000000000000',
  maxGasPriceGwei: parseInt(process.env.MAX_GAS_PRICE_GWEI || '100'),
  maxPriorityFeeGwei: parseInt(process.env.MAX_PRIORITY_FEE_GWEI || '5'),
  slippageTolerance: parseFloat(process.env.SLIPPAGE_TOLERANCE || '0.005'),
  mempoolWebsocketUrl: process.env.MEMPOOL_WEBSOCKET_URL || 'wss://eth-sepolia.g.alchemy.com/v2/YOUR_ALCHEMY_KEY',
  enableFlashbotsMempool: process.env.ENABLE_FLASHBOTS_MEMPOOL === 'true',
  enableEthersMempool: process.env.ENABLE_ETHERS_MEMPOOL === 'true',
  enableSandwichAttacks: process.env.ENABLE_SANDWICH_ATTACKS === 'true',
  enableFrontRunning: process.env.ENABLE_FRONT_RUNNING === 'true',
  enableArbitrage: process.env.ENABLE_ARBITRAGE === 'true',
  enableFlashloanAttacks: process.env.ENABLE_FLASHLOAN_ATTACKS === 'true',
  enableMultiBlockAttacks: process.env.ENABLE_MULTI_BLOCK_ATTACKS === 'true',
  maxBlocksAhead: parseInt(process.env.MAX_BLOCKS_AHEAD || '3'),
  maxPositionSizeEth: parseInt(process.env.MAX_POSITION_SIZE_ETH || '10'),
  emergencyStop: process.env.EMERGENCY_STOP === 'true',
  dryRun: process.env.DRY_RUN !== 'false',
  logLevel: process.env.LOG_LEVEL || 'info',
  logToFile: process.env.LOG_TO_FILE === 'true'
};

// Network-specific addresses
const MAINNET_ADDRESSES = {
  // Mainnet WETH
  WETH: '******************************************',
  // Mainnet tokens
  USDC: '******************************************', // USDC
  USDT: '******************************************', // USDT
  DAI: '******************************************', // DAI
  WBTC: '******************************************', // WBTC
  // Uniswap V2 on Mainnet
  UNISWAP_V2_FACTORY: '******************************************',
  UNISWAP_V3_FACTORY: '******************************************',
  UNISWAP_V2_ROUTER: '******************************************',
  UNISWAP_V3_ROUTER: '******************************************',
  // Aave V3 on Mainnet
  AAVE_POOL_ADDRESSES_PROVIDER: '******************************************',
  AAVE_POOL: '******************************************',
  // Additional DEXs for mainnet
  SUSHISWAP_ROUTER: '******************************************',
  BALANCER_VAULT: '******************************************',
  CURVE_REGISTRY: '******************************************'
};

const SEPOLIA_ADDRESSES = {
  // Sepolia WETH
  WETH: '******************************************',
  // Test tokens on Sepolia
  USDC: '******************************************',
  USDT: '******************************************',
  DAI: '******************************************',
  // Uniswap V2 on Sepolia
  UNISWAP_V2_FACTORY: '******************************************',
  UNISWAP_V3_FACTORY: '******************************************',
  UNISWAP_V2_ROUTER: '******************************************',
  UNISWAP_V3_ROUTER: '******************************************',
  // Aave V3 on Sepolia
  AAVE_POOL_ADDRESSES_PROVIDER: '******************************************',
  AAVE_POOL: '******************************************'
};

// Select addresses based on chain ID
export const ADDRESSES = config.chainId === 1 ? MAINNET_ADDRESSES : SEPOLIA_ADDRESSES;

// Network-specific token lists
const MAINNET_TOKENS = [
  { address: MAINNET_ADDRESSES.WETH, symbol: 'WETH', decimals: 18, name: 'Wrapped Ether' },
  { address: MAINNET_ADDRESSES.USDC, symbol: 'USDC', decimals: 6, name: 'USD Coin' },
  { address: MAINNET_ADDRESSES.USDT, symbol: 'USDT', decimals: 6, name: 'Tether USD' },
  { address: MAINNET_ADDRESSES.DAI, symbol: 'DAI', decimals: 18, name: 'Dai Stablecoin' },
  { address: MAINNET_ADDRESSES.WBTC, symbol: 'WBTC', decimals: 8, name: 'Wrapped Bitcoin' }
];

const SEPOLIA_TOKENS = [
  { address: SEPOLIA_ADDRESSES.WETH, symbol: 'WETH', decimals: 18, name: 'Wrapped Ether' },
  { address: SEPOLIA_ADDRESSES.USDC, symbol: 'USDC', decimals: 6, name: 'USD Coin' }
  // Reduced to 2 tokens to minimize rate limit issues on testnet
];

// Select tokens based on chain ID
export const COMMON_TOKENS = config.chainId === 1 ? MAINNET_TOKENS : SEPOLIA_TOKENS;

export function validateConfig(): void {
  if (config.privateKey === '0x0000000000000000000000000000000000000000000000000000000000000000') {
    console.warn('⚠️  Using default private key - please set PRIVATE_KEY in .env');
  }
  
  if (config.rpcUrl.includes('YOUR_ALCHEMY_KEY')) {
    console.warn('⚠️  Using default RPC URL - please set RPC_URL or ALCHEMY_API_KEY in .env');
  }
  
  if (config.dryRun) {
    console.log('🧪 Running in DRY RUN mode - no real transactions will be sent');
  }
  
  const networkName = config.chainId === 11155111 ? 'Sepolia Testnet' :
                     config.chainId === 1 ? 'Ethereum Mainnet' :
                     `Chain ${config.chainId}`;

  console.log(`🔧 Configuration loaded:
    - Network: ${networkName}
    - Chain ID: ${config.chainId}
    - Min Profit: ${config.minProfitWei} wei
    - Max Gas Price: ${config.maxGasPriceGwei} gwei
    - Slippage Tolerance: ${config.slippageTolerance * 100}%
    - Strategies: ${[
      config.enableSandwichAttacks && 'Sandwich',
      config.enableFrontRunning && 'Front-running',
      config.enableArbitrage && 'Arbitrage',
      config.enableFlashloanAttacks && 'Flashloan'
    ].filter(Boolean).join(', ')}
  `);

  if (config.chainId === 11155111) {
    console.log('🧪 Running on Sepolia testnet - perfect for safe testing!');
  }
}
