/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import {
  Contract,
  ContractFactory,
  ContractTransactionResponse,
  Interface,
} from "ethers";
import type {
  Signer,
  AddressLike,
  ContractDeployTransaction,
  ContractRunner,
} from "ethers";
import type { NonPayableOverrides } from "../../../common";
import type {
  FlashloanArbitrage,
  FlashloanArbitrageInterface,
} from "../../../contracts/FlashloanArbitrage.sol/FlashloanArbitrage";

const _abi = [
  {
    inputs: [
      {
        internalType: "contract IPoolAddressesProvider",
        name: "_addressProvider",
        type: "address",
      },
    ],
    stateMutability: "nonpayable",
    type: "constructor",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "owner",
        type: "address",
      },
    ],
    name: "OwnableInvalidOwner",
    type: "error",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "account",
        type: "address",
      },
    ],
    name: "OwnableUnauthorizedAccount",
    type: "error",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "profit",
        type: "uint256",
      },
    ],
    name: "ArbitrageExecuted",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "premium",
        type: "uint256",
      },
    ],
    name: "FlashloanExecuted",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "previousOwner",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "OwnershipTransferred",
    type: "event",
  },
  {
    inputs: [],
    name: "ADDRESSES_PROVIDER",
    outputs: [
      {
        internalType: "contract IPoolAddressesProvider",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "CHAIN_ID",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "POOL",
    outputs: [
      {
        internalType: "contract IPool",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "SUSHISWAP_ROUTER",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "UNISWAP_V2_ROUTER",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "UNISWAP_V3_ROUTER",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "token",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
    ],
    name: "emergencyWithdraw",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        internalType: "bytes",
        name: "params",
        type: "bytes",
      },
    ],
    name: "executeFlashloanArbitrage",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        internalType: "uint256",
        name: "premium",
        type: "uint256",
      },
      {
        internalType: "address",
        name: "initiator",
        type: "address",
      },
      {
        internalType: "bytes",
        name: "params",
        type: "bytes",
      },
    ],
    name: "executeOperation",
    outputs: [
      {
        internalType: "bool",
        name: "",
        type: "bool",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [],
    name: "owner",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "renounceOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "transferOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "token",
        type: "address",
      },
    ],
    name: "withdrawProfits",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
] as const;

const _bytecode =
  "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";

type FlashloanArbitrageConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: FlashloanArbitrageConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class FlashloanArbitrage__factory extends ContractFactory {
  constructor(...args: FlashloanArbitrageConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override getDeployTransaction(
    _addressProvider: AddressLike,
    overrides?: NonPayableOverrides & { from?: string }
  ): Promise<ContractDeployTransaction> {
    return super.getDeployTransaction(_addressProvider, overrides || {});
  }
  override deploy(
    _addressProvider: AddressLike,
    overrides?: NonPayableOverrides & { from?: string }
  ) {
    return super.deploy(_addressProvider, overrides || {}) as Promise<
      FlashloanArbitrage & {
        deploymentTransaction(): ContractTransactionResponse;
      }
    >;
  }
  override connect(runner: ContractRunner | null): FlashloanArbitrage__factory {
    return super.connect(runner) as FlashloanArbitrage__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): FlashloanArbitrageInterface {
    return new Interface(_abi) as FlashloanArbitrageInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): FlashloanArbitrage {
    return new Contract(address, _abi, runner) as unknown as FlashloanArbitrage;
  }
}
